<?php
namespace App\Controllers;
use CodeIgniter\Controller;
use App\Models\ArtikelModel;
use App\Models\KategoriModel;

class AjaxController extends Controller
{
    public function index()
    {
        return view('ajax/index');
    }

    public function getData()
    {
        try {
            $model = new ArtikelModel();

            // Use simple findAll first to test
            $data = $model->findAll();

            // If that works, try with category
            if (empty($data)) {
                $data = [];
            }

            // Add category information manually if needed
            $kategoriModel = new KategoriModel();
            foreach ($data as &$artikel) {
                if (isset($artikel['id_kategori']) && !empty($artikel['id_kategori'])) {
                    $kategori = $kategoriModel->find($artikel['id_kategori']);
                    $artikel['nama_kategori'] = $kategori ? $kategori['nama_kategori'] : 'Tidak ada kategori';
                } else {
                    $artikel['nama_kategori'] = 'Tidak ada kategori';
                }
            }

            // Kirim data dalam format JSON
            return $this->response->setJSON($data);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'ERROR',
                'message' => 'Gagal mengambil data: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function delete($id)
    {
        try {
            $model = new ArtikelModel();

            // Validate ID
            if (!is_numeric($id) || $id <= 0) {
                return $this->response->setJSON([
                    'status' => 'ERROR',
                    'message' => 'ID artikel tidak valid'
                ]);
            }

            // Check if article exists
            $artikel = $model->find($id);
            if (!$artikel) {
                return $this->response->setJSON([
                    'status' => 'ERROR',
                    'message' => 'Artikel tidak ditemukan'
                ]);
            }

            // Delete associated image file if exists
            if (!empty($artikel['gambar']) && file_exists(FCPATH . 'gambar/' . $artikel['gambar'])) {
                unlink(FCPATH . 'gambar/' . $artikel['gambar']);
            }

            // Delete the article (this will trigger automatic ID reordering)
            $result = $model->delete($id);

            if ($result) {
                return $this->response->setJSON([
                    'status' => 'OK',
                    'message' => 'Artikel berhasil dihapus'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'ERROR',
                    'message' => 'Gagal menghapus artikel'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'ERROR',
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test method to check if AJAX is working
     */
    public function test()
    {
        return $this->response->setJSON([
            'status' => 'OK',
            'message' => 'AJAX is working!',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
