<?php
namespace App\Controllers;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\Request;
use CodeIgniter\HTTP\Response;
use App\Models\ArtikelModel;

class AjaxController extends Controller
{
    public function index()
    {
        return view('ajax/index');
    }

    public function getData()
    {
        $model = new ArtikelModel();
        $data = $model->getAllWithCategory();

        // Kirim data dalam format JSON
        return $this->response->setJSON($data);
    }

    public function delete($id)
    {
        $model = new ArtikelModel();
        
        // Check if article exists
        $artikel = $model->find($id);
        if (!$artikel) {
            $data = [
                'status' => 'ERROR',
                'message' => 'Artikel tidak ditemukan'
            ];
            return $this->response->setJSON($data);
        }

        // Delete associated image file if exists
        if (!empty($artikel['gambar']) && file_exists(FCPATH . 'gambar/' . $artikel['gambar'])) {
            unlink(FCPATH . 'gambar/' . $artikel['gambar']);
        }

        // Delete the article (this will trigger automatic ID reordering)
        $result = $model->delete($id);
        
        if ($result) {
            $data = [
                'status' => 'OK',
                'message' => 'Artikel berhasil dihapus'
            ];
        } else {
            $data = [
                'status' => 'ERROR',
                'message' => 'Gagal menghapus artikel'
            ];
        }
        
        // Kirim data dalam format JSON
        return $this->response->setJSON($data);
    }
}
