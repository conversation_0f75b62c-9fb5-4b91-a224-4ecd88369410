<?php
namespace App\Controllers;
use App\Models\ArtikelModel;
use App\Models\KategoriModel;

class Artikel extends BaseController
{
    public function index()
    {
        $title = 'Daftar Artikel';
        $model = new ArtikelModel();
        $artikel = $model->getArtikelDenganKategori(); // Use the new method
        return view('artikel/index', compact('artikel', 'title'));
    }
    public function view($slug)
    {
        $model = new ArtikelModel();
        $data['artikel'] = $model->where('slug', $slug)->first();
        if (empty($data['artikel'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the article.');
        }
        $data['title'] = $data['artikel']['judul'];
        return view('artikel/detail', $data);
    }

    public function admin_index()
    {
        $title = 'Daftar Artikel';
        $q = $this->request->getVar('q') ?? '';
        $kategori_id = $this->request->getVar('kategori_id') ?? '';
        $model = new ArtikelModel();

        // NEW PAGINATION SYSTEM - Completely redesigned

        // Get total count first
        $totalArticles = $model->countAll();

        // Calculate optimal articles per page to get exactly 2 pages
        $articlesPerPage = ceil($totalArticles / 2);

        // Get current page
        $currentPage = (int)($this->request->getVar('page') ?? 1);

        // Calculate offset
        $offset = ($currentPage - 1) * $articlesPerPage;

        // Get articles for current page with category join
        if (!empty($q) || !empty($kategori_id)) {
            // For search/filter, use different logic
            $db = \Config\Database::connect();
            $builder = $db->table('artikel')
                ->select('artikel.*, kategori.nama_kategori')
                ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left');

            if (!empty($q)) {
                $builder->like('artikel.judul', $q);
            }
            if (!empty($kategori_id)) {
                $builder->where('artikel.id_kategori', $kategori_id);
            }

            $searchResults = $builder->get()->getResultArray();
            $totalSearchResults = count($searchResults);
            $searchPerPage = ceil($totalSearchResults / 2);
            $searchOffset = ($currentPage - 1) * $searchPerPage;
            $artikel = array_slice($searchResults, $searchOffset, $searchPerPage);
            $totalForPagination = $totalSearchResults;
            $perPageForPagination = $searchPerPage;
        } else {
            // Regular pagination with category join
            $db = \Config\Database::connect();
            $artikel = $db->table('artikel')
                ->select('artikel.*, kategori.nama_kategori')
                ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
                ->limit($articlesPerPage, $offset)
                ->get()
                ->getResultArray();
            $totalForPagination = $totalArticles;
            $perPageForPagination = $articlesPerPage;
        }

        // Calculate pagination info
        $totalPages = ceil($totalForPagination / $perPageForPagination);

        // Create custom pagination data
        $paginationData = [
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'totalArticles' => $totalForPagination,
            'articlesPerPage' => $perPageForPagination,
            'hasMultiplePages' => $totalPages > 1,
            'hasPrevious' => $currentPage > 1,
            'hasNext' => $currentPage < $totalPages,
            'searchQuery' => $q,
            'kategori_id' => $kategori_id
        ];

        // Fetch all categories for the filter dropdown
        $kategoriModel = new KategoriModel();

        $data = [
            'title' => $title,
            'q' => $q,
            'kategori_id' => $kategori_id,
            'artikel' => $artikel,
            'pagination' => $paginationData,
            'pager' => null, // We're using custom pagination
            'kategori' => $kategoriModel->findAll()
        ];

        return view('artikel/admin_index', $data);
    }
    public function add()
    {
        $kategoriModel = new KategoriModel();

        // Validation rules
        $validationRules = [
            'judul' => [
                'rules' => 'required|min_length[3]|max_length[255]',
                'errors' => [
                    'required' => 'Judul artikel harus diisi.',
                    'min_length' => 'Judul artikel minimal 3 karakter.',
                    'max_length' => 'Judul artikel maksimal 255 karakter.'
                ]
            ],
            'isi' => [
                'rules' => 'required|min_length[10]',
                'errors' => [
                    'required' => 'Isi artikel harus diisi.',
                    'min_length' => 'Isi artikel minimal 10 karakter.'
                ]
            ],
            'id_kategori' => [
                'rules' => 'required|integer',
                'errors' => [
                    'required' => 'Kategori harus dipilih.',
                    'integer' => 'Kategori tidak valid.'
                ]
            ]
        ];

        if ($this->request->getMethod() == 'post' && $this->validate($validationRules)) {
            $insertData = [
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
                'slug' => url_title($this->request->getPost('judul')),
                'id_kategori' => $this->request->getPost('id_kategori'),
                'status' => 1 // Set as active by default
            ];

            // Handle image upload if file is provided
            $file = $this->request->getFile('gambar');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Validate file type and size
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (in_array($file->getMimeType(), $allowedTypes) && $file->getSize() <= 2048000) { // 2MB max
                    $file->move(ROOTPATH . 'public/gambar');
                    $insertData['gambar'] = $file->getName();
                }
            }

            $model = new ArtikelModel();
            $model->insert($insertData);

            session()->setFlashdata('success', 'Artikel berhasil ditambahkan.');
            return redirect()->to('/admin/artikel');
        } else {
            $data = [
                'title' => "Tambah Artikel",
                'kategori' => $kategoriModel->findAll(),
                'validation' => $this->validator
            ];
            return view('artikel/form_add', $data);
        }
    }

    public function edit($id)
    {
        $model = new ArtikelModel();
        $kategoriModel = new KategoriModel();

        // Check if article exists
        $artikel = $model->find($id);
        if (!$artikel) {
            session()->setFlashdata('error', 'Artikel tidak ditemukan.');
            return redirect()->to('/admin/artikel');
        }

        // Validation rules
        $validationRules = [
            'judul' => [
                'rules' => 'required|min_length[3]|max_length[255]',
                'errors' => [
                    'required' => 'Judul artikel harus diisi.',
                    'min_length' => 'Judul artikel minimal 3 karakter.',
                    'max_length' => 'Judul artikel maksimal 255 karakter.'
                ]
            ],
            'isi' => [
                'rules' => 'required|min_length[10]',
                'errors' => [
                    'required' => 'Isi artikel harus diisi.',
                    'min_length' => 'Isi artikel minimal 10 karakter.'
                ]
            ],
            'id_kategori' => [
                'rules' => 'required|integer',
                'errors' => [
                    'required' => 'Kategori harus dipilih.',
                    'integer' => 'Kategori tidak valid.'
                ]
            ]
        ];

        if ($this->request->getMethod() == 'post' && $this->validate($validationRules)) {
            $updateData = [
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
                'slug' => url_title($this->request->getPost('judul')),
                'id_kategori' => $this->request->getPost('id_kategori')
            ];

            // Handle image upload if new file is provided
            $file = $this->request->getFile('gambar');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Validate file type and size
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (in_array($file->getMimeType(), $allowedTypes) && $file->getSize() <= 2048000) { // 2MB max
                    // Delete old image if exists
                    if (!empty($artikel['gambar']) && file_exists(FCPATH . 'gambar/' . $artikel['gambar'])) {
                        unlink(FCPATH . 'gambar/' . $artikel['gambar']);
                    }

                    $file->move(ROOTPATH . 'public/gambar');
                    $updateData['gambar'] = $file->getName();
                }
            }

            $model->update($id, $updateData);

            session()->setFlashdata('success', 'Artikel berhasil diperbarui.');
            return redirect()->to('/admin/artikel');
        } else {
            $data = [
                'title' => "Edit Artikel",
                'artikel' => $artikel,
                'kategori' => $kategoriModel->findAll(),
                'validation' => $this->validator
            ];
            return view('artikel/form_edit', $data);
        }
    }

    public function delete($id)
    {
        $model = new ArtikelModel();

        // Check if article exists
        $artikel = $model->find($id);
        if (!$artikel) {
            session()->setFlashdata('error', 'Artikel tidak ditemukan.');
            return redirect()->to('/admin/artikel');
        }

        // Delete associated image file if exists
        if (!empty($artikel['gambar']) && file_exists(FCPATH . 'gambar/' . $artikel['gambar'])) {
            unlink(FCPATH . 'gambar/' . $artikel['gambar']);
        }

        // Delete the article (this will trigger ID reordering)
        $model->delete($id);

        session()->setFlashdata('success', 'Artikel berhasil dihapus. ID telah diatur ulang secara berurutan.');
        return redirect()->to('/admin/artikel');
    }

    /**
     * Manual ID reordering - useful for fixing existing data
     * Access via: /admin/artikel/reorder-ids
     */
    public function reorderIds()
    {
        $model = new ArtikelModel();

        try {
            $model->reorderAllIds();
            session()->setFlashdata('success', 'ID artikel berhasil diatur ulang secara berurutan.');
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Gagal mengatur ulang ID: ' . $e->getMessage());
        }

        return redirect()->to('/admin/artikel');
    }

    /**
     * Test ID system - for development/testing purposes
     * Access via: /admin/artikel/test-ids
     */
    public function testIds()
    {
        $model = new ArtikelModel();

        echo "<h2>Testing Sequential ID System</h2>";

        // Show current articles
        $articles = $model->orderBy('id', 'ASC')->findAll();
        echo "<h3>Current Articles:</h3>";
        echo "<table border='1'><tr><th>ID</th><th>Display Order</th><th>Title</th></tr>";
        foreach ($articles as $article) {
            echo "<tr><td>{$article['id']}</td><td>{$article['display_order']}</td><td>{$article['judul']}</td></tr>";
        }
        echo "</table>";

        // Show next available ID
        $nextId = $model->getNextAvailableId();
        echo "<p><strong>Next Available ID:</strong> {$nextId}</p>";

        // Show gaps if any
        $allIds = array_column($articles, 'id');
        $expectedIds = range(1, max($allIds ?: [0]));
        $gaps = array_diff($expectedIds, $allIds);

        if (!empty($gaps)) {
            echo "<p><strong>ID Gaps Found:</strong> " . implode(', ', $gaps) . "</p>";
        } else {
            echo "<p><strong>No ID Gaps Found</strong> - IDs are sequential!</p>";
        }

        echo "<br><a href='" . base_url('/admin/artikel') . "'>Back to Admin</a>";
    }
}
