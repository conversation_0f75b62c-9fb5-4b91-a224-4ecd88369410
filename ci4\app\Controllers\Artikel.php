<?php
namespace App\Controllers;
use App\Models\ArtikelModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Artikel extends BaseController
{
    public function index()
    {
        $model = new ArtikelModel();
        $artikel = $model->findAll();
        return view('artikel/index', ['artikel' => $artikel, 'title' => 'Daftar Artikel']);
    }
    public function view($slug)
    {
    $model = new ArtikelModel();
    $artikel = $model->where([
        'slug' => $slug
    ])->first();

    // Menampilkan error apabila data tidak ada.
    if (!$artikel) {
        throw PageNotFoundException::forPageNotFound();
    }

    $title = $artikel['judul'];
    return view('artikel/detail', compact('artikel', 'title'));
    }

    public function admin_index()
    {
        $title = 'Daftar Artikel';
        $q = $this->request->getVar('q') ?? '';
        $model = new ArtikelModel();

        // NEW PAGINATION SYSTEM - Completely redesigned

        // Get total count first
        $totalArticles = $model->countAll();

        // Calculate optimal articles per page to get exactly 2 pages
        $articlesPerPage = ceil($totalArticles / 2);

        // Get current page
        $currentPage = (int)($this->request->getVar('page') ?? 1);

        // Calculate offset
        $offset = ($currentPage - 1) * $articlesPerPage;

        // Get articles for current page
        if (!empty($q)) {
            // For search, use different logic
            $searchResults = $model->like('judul', $q)->findAll();
            $totalSearchResults = count($searchResults);
            $searchPerPage = ceil($totalSearchResults / 2);
            $searchOffset = ($currentPage - 1) * $searchPerPage;
            $artikel = array_slice($searchResults, $searchOffset, $searchPerPage);
            $totalForPagination = $totalSearchResults;
            $perPageForPagination = $searchPerPage;
        } else {
            // Regular pagination
            $artikel = $model->findAll($articlesPerPage, $offset);
            $totalForPagination = $totalArticles;
            $perPageForPagination = $articlesPerPage;
        }

        // Calculate pagination info
        $totalPages = ceil($totalForPagination / $perPageForPagination);

        // Create custom pagination data
        $paginationData = [
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'totalArticles' => $totalForPagination,
            'articlesPerPage' => $perPageForPagination,
            'hasMultiplePages' => $totalPages > 1,
            'hasPrevious' => $currentPage > 1,
            'hasNext' => $currentPage < $totalPages,
            'searchQuery' => $q
        ];

        $data = [
            'title' => $title,
            'q' => $q,
            'artikel' => $artikel,
            'pagination' => $paginationData,
            'pager' => null // We're not using CodeIgniter's pager anymore
        ];

        return view('artikel/admin_index', $data);
    }
    public function add()
    {
        // validasi data.
        $validation = \Config\Services::validation();
        $validation->setRules(['judul' => 'required']);
        $isDataValid = $validation->withRequest($this->request)->run();
        if ($isDataValid)
        {
            $file = $this->request->getFile('gambar');
            $file->move(ROOTPATH . 'public/gambar');
            $artikel = new ArtikelModel();

            // Get the next available ID to fill gaps
            $nextId = $artikel->getNextAvailableId();

            $artikel->insert([
                'id' => $nextId,
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
                'slug' => url_title($this->request->getPost('judul')),
                'gambar' => $file->getName(),
                'display_order' => $artikel->getNextDisplayOrder(),
            ]);
            return redirect('admin/artikel');
        }
        $title = "Tambah Artikel";
        return view('artikel/form_add', compact('title'));
    }

    public function edit($id)
    {
        $artikel = new ArtikelModel();
        // validasi data.
        $validation = \Config\Services::validation();
        $validation->setRules(['judul' => 'required']);
        $isDataValid = $validation->withRequest($this->request)->run();
        if ($isDataValid)
        {
            $updateData = [
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
            ];

            // Handle image upload if new file is provided
            $file = $this->request->getFile('gambar');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                $file->move(ROOTPATH . 'public/gambar');
                $updateData['gambar'] = $file->getName();
            }

            $artikel->update($id, $updateData);
            return redirect('admin/artikel');
        }
        // ambil data lama
        $data = $artikel->where('id', $id)->first();
        $title = "Edit Artikel";
        return view('artikel/form_edit', compact('title', 'data'));
    }

    public function delete($id)
    {
        $artikel = new ArtikelModel();
        $artikel->delete($id);

        // Reorder display numbers after deletion
        $artikel->reorderDisplayNumbers();

        // Reorder IDs to fill gaps after deletion
        $artikel->reorderIds();

        return redirect('admin/artikel');
    }
}
