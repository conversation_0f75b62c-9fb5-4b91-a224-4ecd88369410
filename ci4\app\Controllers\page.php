<?php
namespace App\Controllers;

class Page extends BaseController
{
    public function about()
    {
        return view('about', [
            'title'   => 'Halaman About',
            'content' => 'Ini adalah halaman about yang menjelaskan tentang isi halaman ini.'
        ]);
    }

    public function contact()
    {
        return view('contact', [
            'title' => 'Halaman Contact',
            'content' => 'Ini adalah halaman kontak. Anda dapat menghubungi kami melalui informasi yang tersedia di halaman ini.'
        ]);
    }

    public function faqs()
    {
        echo "Ini halaman FAQ";
    }

    public function services() {
        return view('services', [
            'title' => '💼 Halaman Services',
            'content' => 'Ka<PERSON> menyed<PERSON> berbagai layanan, mulai dari konsultasi IT hingga pengembangan software. Hubungi kami untuk informasi lebih lanjut!'
        ]);
    }
    
    public function artikel() {
        return view('artikel', [
            'title' => '📰 Halaman Artikel',
            'content' => 'Selamat datang di halaman artikel. Di sini Anda dapat membaca berbagai artikel menarik yang kami sajikan.'
        ]);
    }
}
