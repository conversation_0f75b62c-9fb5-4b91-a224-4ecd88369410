<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddDisplayOrderToArtikel extends Migration
{
    public function up()
    {
        // Tambahkan kolom display_order
        $this->forge->addColumn('artikel', [
            'display_order' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'id'
            ]
        ]);

        // Isi display_order untuk data yang sudah ada
        $db = \Config\Database::connect();
        $articles = $db->query("SELECT id FROM artikel ORDER BY id ASC")->getResultArray();

        $order = 1;
        foreach ($articles as $article) {
            $db->query("UPDATE artikel SET display_order = ? WHERE id = ?", [$order, $article['id']]);
            $order++;
        }
    }

    public function down()
    {
        // Hapus kolom display_order
        $this->forge->dropColumn('artikel', 'display_order');
    }
}
