<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateUserTable extends Migration
{
    public function up()
    {
        // Membuat tabel user
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'username' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
            ],
            'useremail' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
            'userpassword' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
        ]);

        // Menambahkan primary key
        $this->forge->addPrimaryKey('id');

        // Membuat tabel
        $this->forge->createTable('user');
    }

    public function down()
    {
        // Menghapus tabel user
        $this->forge->dropTable('user');
    }
}
