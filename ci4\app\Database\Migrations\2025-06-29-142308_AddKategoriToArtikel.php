<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddKategoriToArtikel extends Migration
{
    public function up()
    {
        // Add id_kategori column to artikel table
        $this->forge->addColumn('artikel', [
            'id_kategori' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'gambar'
            ]
        ]);

        // Add foreign key constraint
        $this->forge->addForeignKey('id_kategori', 'kategori', 'id_kategori', 'CASCADE', 'SET NULL');
    }

    public function down()
    {
        // Drop foreign key constraint first
        $this->forge->dropForeignKey('artikel', 'artikel_id_kategori_foreign');

        // Drop the column
        $this->forge->dropColumn('artikel', 'id_kategori');
    }
}
