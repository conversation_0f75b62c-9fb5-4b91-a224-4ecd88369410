<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class MoreArtikelSeeder extends Seeder
{
    public function run()
    {
        $data = [];
        
        // Generate 15 artikel tambahan untuk testing pagination (total akan jadi 40 artikel = 4 halaman)
        for ($i = 26; $i <= 40; $i++) {
            $data[] = [
                'judul' => "Artikel Sample $i",
                'isi' => "Ini adalah isi artikel sample nomor $i. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
                'slug' => "artikel-sample-$i",
                'status' => 1,
                'gambar' => '',
                'display_order' => $i
            ];
        }
        
        // Insert data ke tabel artikel
        $this->db->table('artikel')->insertBatch($data);
    }
}
