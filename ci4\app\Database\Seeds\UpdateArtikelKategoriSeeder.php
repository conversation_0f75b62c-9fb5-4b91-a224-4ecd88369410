<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class UpdateArtikelKategoriSeeder extends Seeder
{
    public function run()
    {
        // Update existing articles with categories
        $updates = [
            ['id' => 1, 'id_kategori' => 2], // Olahraga
            ['id' => 2, 'id_kategori' => 2], // Olahraga
            ['id' => 3, 'id_kategori' => 2], // Olahraga
            ['id' => 4, 'id_kategori' => 2], // Olahraga
            ['id' => 5, 'id_kategori' => 2], // Olahraga
            ['id' => 6, 'id_kategori' => 2], // Olahraga
            ['id' => 7, 'id_kategori' => 2], // Olahraga
            ['id' => 8, 'id_kategori' => 2], // Olahraga
            ['id' => 11, 'id_kategori' => 1], // Teknologi
        ];

        foreach ($updates as $update) {
            $this->db->table('artikel')
                ->where('id', $update['id'])
                ->update(['id_kategori' => $update['id_kategori']]);
        }
    }
}
