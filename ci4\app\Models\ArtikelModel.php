<?php

namespace App\Models;

use CodeIgniter\Model;

class ArtikelModel extends Model
{
    protected $table            = 'artikel';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = false; // Disable auto-increment to manage IDs manually
    protected $allowedFields    = ['id', 'judul', 'isi', 'status', 'slug', 'gambar', 'id_kategori', 'display_order'];

    /**
     * Get the next available ID (optimized for automatic sequential assignment)
     * This ensures sequential IDs without gaps
     */
    public function getNextAvailableId()
    {
        // Check if table is empty first (fastest check)
        $count = $this->countAll();
        if ($count == 0) {
            return 1;
        }

        // For performance, simply return count + 1 since we maintain sequential IDs
        // This works because we automatically reorder after every deletion
        return $count + 1;
    }

    /**
     * Insert with automatic sequential ID assignment
     * Override the insert method to assign sequential IDs automatically
     */
    public function insert($data = null, bool $returnID = true)
    {
        if (is_array($data)) {
            try {
                // Get the next available ID (optimized for sequential assignment)
                $nextId = $this->getNextAvailableId();
                $data['id'] = $nextId;

                // Set display_order to match ID for consistency
                $data['display_order'] = $nextId;

            } catch (\Exception $e) {
                // Log error and let parent handle with auto-increment as fallback
                log_message('error', 'Failed to assign sequential ID: ' . $e->getMessage());
                unset($data['id']); // Remove ID to let database handle it
            }
        }

        return parent::insert($data, $returnID);
    }

    /**
     * Delete and automatically maintain sequential IDs
     * Override delete method to trigger automatic ID reordering
     */
    public function delete($id = null, bool $purge = false)
    {
        // Perform the deletion
        $result = parent::delete($id, $purge);

        if ($result) {
            try {
                // Automatically reorder all IDs to maintain sequence
                $this->reorderAllIds();
            } catch (\Exception $e) {
                // Log error but don't fail the deletion
                log_message('error', 'Failed to reorder IDs after deletion: ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Reorder all IDs to be sequential (1, 2, 3, 4...)
     * Optimized for automatic background operation
     */
    public function reorderAllIds()
    {
        $db = \Config\Database::connect();

        // Get all articles ordered by current ID
        $articles = $this->orderBy('id', 'ASC')->findAll();

        if (empty($articles)) {
            // Reset auto-increment to 1 if no articles
            $db->query("ALTER TABLE artikel AUTO_INCREMENT = 1");
            return;
        }

        // Check if reordering is actually needed (performance optimization)
        $needsReordering = false;
        $expectedId = 1;
        foreach ($articles as $article) {
            if ($article['id'] != $expectedId) {
                $needsReordering = true;
                break;
            }
            $expectedId++;
        }

        // If already sequential, no need to reorder
        if (!$needsReordering) {
            return;
        }

        // Perform reordering in a transaction for data integrity
        $db->transStart();

        try {
            // Disable foreign key checks temporarily
            $db->query('SET FOREIGN_KEY_CHECKS = 0');

            // Use a more efficient approach: update IDs in reverse order to avoid conflicts
            $totalArticles = count($articles);

            // First, move all IDs to temporary high values to avoid conflicts
            for ($i = 0; $i < $totalArticles; $i++) {
                $oldId = $articles[$i]['id'];
                $tempId = $totalArticles + $i + 1000; // Use high temporary ID
                $db->query("UPDATE artikel SET id = ?, display_order = ? WHERE id = ?", [$tempId, $tempId, $oldId]);
            }

            // Then, assign sequential IDs
            for ($i = 0; $i < $totalArticles; $i++) {
                $tempId = $totalArticles + $i + 1000;
                $newId = $i + 1;
                $db->query("UPDATE artikel SET id = ?, display_order = ? WHERE id = ?", [$newId, $newId, $tempId]);
            }

            // Reset auto-increment
            $nextAutoIncrement = $totalArticles + 1;
            $db->query("ALTER TABLE artikel AUTO_INCREMENT = {$nextAutoIncrement}");

            // Re-enable foreign key checks
            $db->query('SET FOREIGN_KEY_CHECKS = 1');

            $db->transComplete();

        } catch (\Exception $e) {
            $db->transRollback();
            $db->query('SET FOREIGN_KEY_CHECKS = 1');
            throw $e;
        }
    }

    /**
     * Get next display order number (same as ID for consistency)
     */
    public function getNextDisplayOrder()
    {
        return $this->getNextAvailableId();
    }

    public function getArtikelDenganKategori()
    {
        return $this->db->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->orderBy('artikel.id', 'ASC')
            ->get()
            ->getResultArray();
    }

    /**
     * Get all articles with category information for AJAX requests
     */
    public function getAllWithCategory()
    {
        return $this->db->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->orderBy('artikel.id', 'ASC')
            ->get()
            ->getResultArray();
    }
}
