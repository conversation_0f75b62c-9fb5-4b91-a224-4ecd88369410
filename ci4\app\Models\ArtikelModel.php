<?php

namespace App\Models;

use CodeIgniter\Model;

class ArtikelModel extends Model
{
    protected $table            = 'artikel';
    protected $primaryKey       = 'id';
    protected $allowedFields    = ['judul', 'isi', 'status', 'slug', 'gambar', 'display_order'];

    /**
     * Get next display order number
     */
    public function getNextDisplayOrder()
    {
        $result = $this->selectMax('display_order')->first();
        return ($result['display_order'] ?? 0) + 1;
    }

    /**
     * Reorder display numbers after deletion
     */
    public function reorderDisplayNumbers()
    {
        $articles = $this->orderBy('display_order', 'ASC')->findAll();
        $order = 1;

        foreach ($articles as $article) {
            if ($article['display_order'] != $order) {
                $this->update($article['id'], ['display_order' => $order]);
            }
            $order++;
        }
    }
}
