<?php

namespace App\Models;

use CodeIgniter\Model;

class ArtikelModel extends Model
{
    protected $table            = 'artikel';
    protected $primaryKey       = 'id';
    protected $allowedFields    = ['id', 'judul', 'isi', 'status', 'slug', 'gambar', 'display_order', 'id_kategori'];

    /**
     * Get next display order number
     */
    public function getNextDisplayOrder()
    {
        $result = $this->selectMax('display_order')->first();
        return ($result['display_order'] ?? 0) + 1;
    }

    /**
     * Reorder display numbers after deletion
     */
    public function reorderDisplayNumbers()
    {
        $articles = $this->orderBy('display_order', 'ASC')->findAll();
        $order = 1;

        foreach ($articles as $article) {
            if ($article['display_order'] != $order) {
                $this->update($article['id'], ['display_order' => $order]);
            }
            $order++;
        }
    }

    /**
     * Get the next available ID (fills gaps from deleted records)
     */
    public function getNextAvailableId()
    {
        $db = \Config\Database::connect();

        // Find the first gap in the ID sequence
        $query = $db->query("
            SELECT t1.id + 1 AS gap_start
            FROM artikel t1
            LEFT JOIN artikel t2 ON t1.id + 1 = t2.id
            WHERE t2.id IS NULL
            ORDER BY t1.id
            LIMIT 1
        ");

        $result = $query->getRow();

        if ($result) {
            return $result->gap_start;
        }

        // If no gaps found, get the next ID after the highest
        $maxId = $this->selectMax('id')->first();
        return ($maxId['id'] ?? 0) + 1;
    }

    /**
     * Reorder IDs to fill gaps after deletion
     */
    public function reorderIds()
    {
        $db = \Config\Database::connect();

        // Get all articles ordered by current ID
        $articles = $this->orderBy('id', 'ASC')->findAll();

        if (empty($articles)) {
            return;
        }

        // Disable foreign key checks temporarily
        $db->query('SET FOREIGN_KEY_CHECKS = 0');

        // Create a temporary mapping of old ID to new ID
        $idMapping = [];
        $newId = 1;

        foreach ($articles as $article) {
            $oldId = $article['id'];
            $idMapping[$oldId] = $newId;
            $newId++;
        }

        // Update each record with new sequential ID
        foreach ($articles as $article) {
            $oldId = $article['id'];
            $newId = $idMapping[$oldId];

            if ($oldId != $newId) {
                // Update the record with new ID
                $db->query("UPDATE artikel SET id = ? WHERE id = ?", [$newId, $oldId]);
            }
        }

        // Reset auto-increment to the next available number
        $nextAutoIncrement = count($articles) + 1;
        $db->query("ALTER TABLE artikel AUTO_INCREMENT = ?", [$nextAutoIncrement]);

        // Re-enable foreign key checks
        $db->query('SET FOREIGN_KEY_CHECKS = 1');
    }

    /**
     * Get articles with category information using JOIN
     */
    public function getArtikelDenganKategori()
    {
        return $this->db->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->get()
            ->getResultArray();
    }
}
