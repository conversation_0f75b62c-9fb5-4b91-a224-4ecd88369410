<?php

namespace App\Models;

use CodeIgniter\Model;

class ArtikelModel extends Model
{
    protected $table            = 'artikel';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = false; // Disable auto-increment to manage IDs manually
    protected $allowedFields    = ['id', 'judul', 'isi', 'status', 'slug', 'gambar', 'id_kategori', 'display_order'];

    /**
     * Get the next available ID (fills gaps from deleted records)
     * This ensures sequential IDs without gaps
     */
    public function getNextAvailableId()
    {
        $db = \Config\Database::connect();

        // Start from ID 1 and find the first missing number
        $query = $db->query("
            SELECT COALESCE(MIN(t1.id + 1), 1) AS next_id
            FROM artikel t1
            LEFT JOIN artikel t2 ON t1.id + 1 = t2.id
            WHERE t2.id IS NULL AND t1.id + 1 <= (SELECT COALESCE(MAX(id), 0) + 1 FROM artikel)
        ");

        $result = $query->getRow();

        if ($result && $result->next_id) {
            return (int)$result->next_id;
        }

        // If no gaps found, check if table is empty
        $count = $this->countAll();
        if ($count == 0) {
            return 1;
        }

        // Get the next ID after the highest
        $maxId = $this->selectMax('id')->first();
        return ($maxId['id'] ?? 0) + 1;
    }

    /**
     * Insert with sequential ID management
     * Override the insert method to assign sequential IDs
     */
    public function insert($data = null, bool $returnID = true)
    {
        if (is_array($data)) {
            // Get the next available ID
            $nextId = $this->getNextAvailableId();
            $data['id'] = $nextId;

            // Set display_order to match ID for consistency
            $data['display_order'] = $nextId;
        }

        return parent::insert($data, $returnID);
    }

    /**
     * Delete and reorder IDs to maintain sequence
     * Override delete method to trigger ID reordering
     */
    public function delete($id = null, bool $purge = false)
    {
        $result = parent::delete($id, $purge);

        if ($result) {
            // Reorder all IDs to fill gaps after deletion
            $this->reorderAllIds();
        }

        return $result;
    }

    /**
     * Reorder all IDs to be sequential (1, 2, 3, 4...)
     * This method ensures no gaps in the ID sequence
     */
    public function reorderAllIds()
    {
        $db = \Config\Database::connect();

        // Get all articles ordered by current ID
        $articles = $this->orderBy('id', 'ASC')->findAll();

        if (empty($articles)) {
            // Reset auto-increment to 1 if no articles
            $db->query("ALTER TABLE artikel AUTO_INCREMENT = 1");
            return;
        }

        // Disable foreign key checks temporarily
        $db->query('SET FOREIGN_KEY_CHECKS = 0');

        // Create temporary table to store new data
        $db->query("CREATE TEMPORARY TABLE artikel_temp LIKE artikel");

        // Insert articles with sequential IDs
        $newId = 1;
        foreach ($articles as $article) {
            $article['id'] = $newId;
            $article['display_order'] = $newId;

            // Build insert query
            $fields = implode(', ', array_keys($article));
            $placeholders = implode(', ', array_fill(0, count($article), '?'));
            $db->query("INSERT INTO artikel_temp ({$fields}) VALUES ({$placeholders})", array_values($article));

            $newId++;
        }

        // Replace original table with reordered data
        $db->query("DELETE FROM artikel");
        $db->query("INSERT INTO artikel SELECT * FROM artikel_temp");
        $db->query("DROP TEMPORARY TABLE artikel_temp");

        // Reset auto-increment to the next available number
        $nextAutoIncrement = count($articles) + 1;
        $db->query("ALTER TABLE artikel AUTO_INCREMENT = {$nextAutoIncrement}");

        // Re-enable foreign key checks
        $db->query('SET FOREIGN_KEY_CHECKS = 1');
    }

    /**
     * Get next display order number (same as ID for consistency)
     */
    public function getNextDisplayOrder()
    {
        return $this->getNextAvailableId();
    }

    public function getArtikelDenganKategori()
    {
        return $this->db->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori')
            ->get()
            ->getResultArray();
    }
}
