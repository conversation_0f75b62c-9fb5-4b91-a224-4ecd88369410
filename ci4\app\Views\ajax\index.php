<?= $this->include('template/header'); ?>
<h1>Data Artikel</h1>
<table class="table-data" id="artikelTable">
    <thead>
        <tr>
            <th>ID</th>
            <th>Judul</th>
            <th><PERSON><PERSON>i</th>
            <th>Status</th>
            <th>Aksi</th>
        </tr>
    </thead>
    <tbody></tbody>
</table>

<script src="<?= base_url('assets/js/jquery-3.7.1.min.js') ?>"></script>
<script>
$(document).ready(function() {
    // Function to display a loading message while data is fetched
    function showLoadingMessage() {
        $('#artikelTable tbody').html('<tr><td colspan="5">Loading data...</td></tr>');
    }

    // Buat fungsi load data
    function loadData() {
        showLoadingMessage(); // Display loading message initially
        
        // Lakukan request AJAX ke URL getData
        $.ajax({
            url: "<?= base_url('ajax/getData') ?>",
            method: "GET",
            dataType: "json",
            success: function(data) {
                console.log('Data received:', data);
                // Tampilkan data yang diterima dari server
                var tableBody = "";
                for (var i = 0; i < data.length; i++) {
                    var row = data[i];
                    tableBody += '<tr>';
                    tableBody += '<td>' + row.id + '</td>';
                    tableBody += '<td>' + row.judul + '</td>';

                    // Add category column
                    tableBody += '<td>' + (row.nama_kategori || 'Tidak ada kategori') + '</td>';

                    // Add status column with proper status display
                    if (row.status == 1) {
                        tableBody += '<td><span class="status status-active">Aktif</span></td>';
                    } else {
                        tableBody += '<td><span class="status status-inactive">Draft</span></td>';
                    }

                    tableBody += '<td>';
                    // Replace with your desired actions (e.g., edit, delete)
                    tableBody += '<a href="<?= base_url('artikel/edit/') ?>' + row.id + '" class="btn btn-primary">Edit</a>';
                    tableBody += ' <a href="#" class="btn btn-danger btn-delete" data-id="' + row.id + '">Delete</a>';
                    tableBody += '</td>';
                    tableBody += '</tr>';
                }
                $('#artikelTable tbody').html(tableBody);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('AJAX Error:', jqXHR.responseText);
                console.log('Status:', textStatus);
                console.log('Error:', errorThrown);
                $('#artikelTable tbody').html('<tr><td colspan="5">Error loading data: ' + textStatus + '<br>Response: ' + jqXHR.responseText + '</td></tr>');
            }
        });
    }

    // Load data when page is ready
    loadData();

    // Implement actions for buttons (e.g., delete confirmation)
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        
        // Add confirmation dialog or handle deletion logic here
        if (confirm('Apakah Anda yakin ingin menghapus artikel ini?')) {
            $.ajax({
                url: "<?= base_url('ajax/delete/') ?>" + id,
                method: "POST",
                data: {
                    _method: "DELETE"
                },
                dataType: "json",
                success: function(response) {
                    if (response.status === 'OK') {
                        alert('Artikel berhasil dihapus');
                        loadData(); // Reload data to reflect changes
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('AJAX Error:', jqXHR.responseText);
                    alert('Error deleting article: ' + textStatus + ' ' + errorThrown);
                }
            });
        }
        console.log('Delete button clicked for ID:', id);
    });
});
</script>
<?= $this->include('template/footer'); ?>
