<?= $this->include('template/admin_header'); ?>

<div class="admin-main">
    <div class="content-card">
        <h2>Daftar Artikel</h2>

        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success">
                <?= session()->getFlashdata('success'); ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error'); ?>
            </div>
        <?php endif; ?>

        <form method="get" class="form-search">
            <input type="text" name="q" value="<?= $q; ?>" placeholder="Cari data">
            <select name="kategori_id">
                <option value="">Semua Kategori</option>
                <?php foreach ($kategori as $k): ?>
                    <option value="<?= $k['id_kategori']; ?>" <?= ($kategori_id == $k['id_kategori']) ? 'selected' : ''; ?>><?= $k['nama_kategori']; ?></option>
                <?php endforeach; ?>
            </select>
            <input type="submit" value="Cari" class="btn btn-primary">
            <a href="<?= base_url('/admin/artikel/reorder-ids'); ?>"
               class="btn btn-secondary"
               onclick="return confirm('Yakin ingin mengatur ulang ID artikel secara berurutan? Ini akan mengubah semua ID artikel.');"
               style="margin-left: 10px;">
                Atur Ulang ID
            </a>
        </form>

        <table class="table">
        <thead>
            <tr>
                <th style="width: 50px;">No.</th>
                <th style="width: 50%;">Judul</th>
                <th style="width: 120px;">Kategori</th>
                <th style="width: 80px;">Status</th>
                <th style="width: 140px;">Aksi</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($artikel): ?>
                <?php foreach ($artikel as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['display_order'] ?? $row['id']); ?></td>
                        <td>
                            <strong><?= htmlspecialchars($row['judul']); ?></strong>
                            <br>
                            <small style="color: #666;"><?= nl2br(htmlspecialchars(substr($row['isi'], 0, 60))); ?>...</small>
                        </td>
                        <td><?= htmlspecialchars($row['nama_kategori'] ?? 'Tidak ada kategori'); ?></td>
                        <td>
                            <?php if ($row['status'] == 1): ?>
                                <span class="status-badge status-active">Aktif</span>
                            <?php else: ?>
                                <span class="status-badge status-inactive">Draft</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a class="btn-ubah" href="<?= base_url('/admin/artikel/edit/' . $row['id']); ?>">
                                Edit
                            </a>
                            <a class="btn-hapus" onclick="return confirm('Yakin ingin menghapus artikel ini?');" href="<?= base_url('/admin/artikel/delete/' . $row['id']); ?>">
                                Hapus
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5" style="text-align: center; padding: 40px; color: #666;">
                        <strong>Belum ada artikel</strong><br>
                        <small>Klik tombol "Tambah Artikel" untuk membuat artikel pertama</small>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- PAGINATION SYSTEM -->
    <div class="pagination-wrapper">
        <?php if (isset($pagination)) : ?>
            <?= view('pager/new_pagination', ['pagination' => $pagination]); ?>
        <?php endif ?>
    </div>
    </div>
</div>

<?= $this->include('template/admin_footer'); ?>