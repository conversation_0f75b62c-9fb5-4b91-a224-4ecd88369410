<?= $this->include('template/admin_header'); ?>

<div class="content-card">
    <h2><?= $title; ?></h2>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger">
            <?= session()->getFlashdata('error'); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($validation) && $validation->getErrors()): ?>
        <div class="alert alert-danger">
            <ul style="margin: 0; padding-left: 20px;">
                <?php foreach ($validation->getErrors() as $error): ?>
                    <li><?= $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <form action="" method="post" enctype="multipart/form-data" class="form-container">
        <div class="form-group">
            <label for="judul">Judul Artikel</label>
            <input type="text" name="judul" id="judul" value="<?= htmlspecialchars($artikel['judul']); ?>" placeholder="Masukkan judul artikel..." required>
        </div>

        <div class="form-group">
            <label for="isi">Isi Artikel</label>
            <textarea name="isi" id="isi" rows="10" placeholder="Tulis konten artikel di sini..." required><?= htmlspecialchars($artikel['isi']); ?></textarea>
        </div>

        <div class="form-group">
            <label for="id_kategori">Kategori</label>
            <select name="id_kategori" id="id_kategori" required>
                <option value="">Pilih Kategori</option>
                <?php foreach($kategori as $k): ?>
                    <option value="<?= $k['id_kategori']; ?>" <?= ($artikel['id_kategori'] == $k['id_kategori']) ? 'selected' : ''; ?>><?= $k['nama_kategori']; ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="form-group">
            <label for="gambar">Gambar</label>
            <?php if(!empty($artikel['gambar']) && file_exists(FCPATH . 'gambar/' . $artikel['gambar'])): ?>
                <div class="current-image" style="margin-bottom: 15px;">
                    <p><strong>Gambar saat ini:</strong></p>
                    <img src="<?= base_url('/gambar/' . $artikel['gambar']);?>" alt="Current image" style="max-width: 300px; height: auto; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">
                    <p><small style="color: #666;">Pilih file baru untuk mengganti gambar, atau biarkan kosong untuk mempertahankan gambar saat ini.</small></p>
                </div>
            <?php endif; ?>
            <input type="file" name="gambar" id="gambar" accept="image/*">
            <small class="form-text">Pilih file gambar baru untuk mengganti gambar yang ada (opsional)</small>
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-large btn-primary">
                Update
            </button>
            <a href="<?= base_url('/admin/artikel'); ?>" class="btn btn-large" style="background-color: #6c757d; margin-left: 10px;">
                Kembali
            </a>
        </div>
    </form>
</div>

<?= $this->include('template/admin_footer'); ?>
