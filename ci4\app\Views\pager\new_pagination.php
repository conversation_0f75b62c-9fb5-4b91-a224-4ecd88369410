<?php
/**
 * COMPLETELY NEW PAGINATION SYSTEM
 * Custom pagination that works independently of CodeIgniter's pager
 * Receives pagination data from controller instead of pager object
 */

// This template now expects $pagination data from controller, not $pager
if (!isset($pagination)) {
    echo "<!-- No pagination data provided -->";
    return;
}

// Extract pagination data
$currentPage = $pagination['currentPage'];
$totalPages = $pagination['totalPages'];
$totalArticles = $pagination['totalArticles'];
$articlesPerPage = $pagination['articlesPerPage'];
$hasMultiplePages = $pagination['hasMultiplePages'];
$searchQuery = $pagination['searchQuery'] ?? '';
$kategoriId = $pagination['kategori_id'] ?? '';

// Build base URL
$baseUrl = base_url('admin/artikel');
$queryParams = [];
if (!empty($searchQuery)) {
    $queryParams['q'] = $searchQuery;
}
if (!empty($kategoriId)) {
    $queryParams['kategori_id'] = $kategoriId;
}

// Function to build page URL
function buildPageUrl($baseUrl, $queryParams, $pageNum) {
    $params = $queryParams;
    $params['page'] = $pageNum;
    $queryString = http_build_query($params);
    return $baseUrl . ($queryString ? '?' . $queryString : '');
}
?>

<!-- Pagination Info -->
<div class="pagination-info" style="text-align: center; margin: 15px 0; color: #6c757d; font-size: 14px;">
    Showing <?= (($currentPage - 1) * $articlesPerPage) + 1 ?> to <?= min($currentPage * $articlesPerPage, $totalArticles) ?> of <?= $totalArticles ?> articles
</div>

<!-- Pagination Navigation -->
<?php if ($hasMultiplePages) : ?>
<nav aria-label="Page navigation" class="custom-pagination">
    <div class="pagination-container" style="display: flex; justify-content: center; align-items: center; gap: 10px; margin: 20px 0;">
        
        <!-- Previous Button -->
        <?php if ($currentPage > 1) : ?>
            <a href="<?= buildPageUrl($baseUrl, $queryParams, $currentPage - 1) ?>" 
               class="page-btn prev-btn"
               style="background: #f8f9fa; color: #495057; padding: 10px 15px; border-radius: 6px; text-decoration: none; border: 1px solid #dee2e6; font-weight: 500; transition: all 0.2s ease;">
                ← Previous
            </a>
        <?php endif; ?>
        
        <!-- Page Numbers -->
        <div class="page-numbers" style="display: flex; gap: 5px;">
            <?php for ($i = 1; $i <= $totalPages; $i++) : ?>
                <?php if ($i == $currentPage) : ?>
                    <span class="page-btn active" 
                          style="background: #007bff; color: white; padding: 10px 15px; border-radius: 6px; font-weight: 600; min-width: 45px; text-align: center; border: 1px solid #007bff;">
                        <?= $i ?>
                    </span>
                <?php else : ?>
                    <a href="<?= buildPageUrl($baseUrl, $queryParams, $i) ?>" 
                       class="page-btn"
                       style="background: #ffffff; color: #495057; padding: 10px 15px; border-radius: 6px; text-decoration: none; border: 1px solid #dee2e6; font-weight: 500; min-width: 45px; text-align: center; transition: all 0.2s ease;">
                        <?= $i ?>
                    </a>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
        
        <!-- Next Button -->
        <?php if ($currentPage < $totalPages) : ?>
            <a href="<?= buildPageUrl($baseUrl, $queryParams, $currentPage + 1) ?>" 
               class="page-btn next-btn"
               style="background: #f8f9fa; color: #495057; padding: 10px 15px; border-radius: 6px; text-decoration: none; border: 1px solid #dee2e6; font-weight: 500; transition: all 0.2s ease;">
                Next →
            </a>
        <?php endif; ?>
        
    </div>
</nav>

<!-- CSS for hover effects -->
<style>
.custom-pagination .page-btn:hover {
    background: #e9ecef !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-pagination .page-btn.active:hover {
    background: #0056b3 !important;
    transform: none;
}

@media (max-width: 576px) {
    .pagination-container {
        flex-wrap: wrap;
        gap: 5px !important;
    }
    
    .page-btn {
        padding: 8px 12px !important;
        font-size: 14px !important;
        min-width: 40px !important;
    }
    
    .prev-btn, .next-btn {
        order: 2;
        flex: 1;
        text-align: center;
        max-width: 100px;
    }
    
    .page-numbers {
        order: 1;
        width: 100%;
        justify-content: center;
    }
}
</style>

<?php else : ?>
<!-- Single page - no pagination needed -->
<div class="pagination-info" style="text-align: center; margin: 15px 0; color: #6c757d; font-size: 14px;">
    All <?= $totalArticles ?> articles shown
</div>
<?php endif; ?>
