/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,600;0,700;0,800;1,300;1,400;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans+Condensed:ital,wght@0,300;0,700;1,300&display=swap');

/* Reset CSS */
* {
  margin: 0;
  padding: 0;
}

body {
  line-height: 1;
  font-size: 100%;
  font-family: 'Open Sans', sans-serif;
  color: #5a5a5a;
}

#container {
  width: 980px;
  margin: 0 auto;
  box-shadow: 0 0 1em #cccccc;
}

/* Admin layout container adjustments - STICKY FOOTER SETUP */
.admin-layout #container {
  width: 100%;
  max-width: none;
  margin: 0;
  box-shadow: none;
  padding: 0;
  /* Sticky footer setup */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* Header */
header {
  padding: 20px;
}

header h1 {
  margin: 20px 10px;
  color: #b5b5b5;
}

/* Admin layout header */
.admin-layout header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 20px;
  margin: 0;
}

.admin-layout header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

/* Navigation */
nav {
  display: block;
  background-color: #007bff;
  border-bottom: 1px solid #0056b3;
  margin: 0;
  padding: 0;
}

/* Admin layout navigation */
.admin-layout nav {
  width: 100%;
  margin: 0;
  padding: 0;
}

nav a {
  padding: 12px 20px;
  display: inline-block;
  color: #ffffff;
  font-size: 14px;
  text-decoration: none;
  font-weight: 400;
  transition: all 0.2s ease;
}

nav a:first-child {
  padding-left: 20px;
}

nav a:last-child {
  padding-right: 20px;
}

nav a.active,
nav a:hover {
  background-color: #0056b3;
  color: #ffffff;
}

/* Hero Panel */
#hero {
  background-color: #e4e4e5;
  padding: 50px 20px;
  margin-bottom: 20px;
}

#hero h1 {
  margin-bottom: 20px;
  font-size: 35px;
}

#hero p {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 25px;
}

/* Main Content */
#wrapper {
  margin: 0;
  display: flex;
  gap: 20px;
  padding: 20px;
}

#main {
  flex: 1;
  max-width: 70%;
}

/* Sidebar */
#sidebar {
  width: 280px;
  flex-shrink: 0;
}

/* Widget */
.widget-box {
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
  background: #fff;
}

.widget-box .title {
  padding: 12px 16px;
  background-color: #007bff;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.widget-box ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.widget-box li {
  border-bottom: 1px solid #f0f0f0;
}

.widget-box li:last-child {
  border-bottom: none;
}

.widget-box li a {
  padding: 10px 16px;
  color: #333;
  display: block;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.widget-box li:hover a {
  background-color: #f8f9fa;
}

.widget-box p {
  padding: 16px;
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Footer */
footer {
  clear: both;
  background-color: #1d1d1d;
  padding: 20px;
  color: #eee;
}

/* Box */
.box {
  display: block;
  float: left;
  width: 33.3333%;
  box-sizing: border-box;
  padding: 0 10px;
  text-align: center;
}

.box h3 {
  margin: 15px 0;
}

.box p {
  line-height: 20px;
  font-size: 14px;
  margin-bottom: 15px;
}

.box img {
  border: 0;
  vertical-align: middle;
}

.image-circle {
  border-radius: 50%;
}

/* Row */
.row {
  margin: 0 -10px;
  box-sizing: border-box;
}

.row:after,
.row:before,
.entry:after,
.entry:before {
  content: '';
  display: table;
}

.row:after,
.entry:after {
  clear: both;
}

/* Divider */
.divider {
  border: 0;
  border-top: 1px solid #eeeeee;
  margin: 40px 0;
}

/* Entry */
.entry {
  margin: 20px 0;
  padding: 20px;
  background: #fff;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.entry h2 {
  margin-bottom: 15px;
  font-size: 22px;
  font-weight: 500;
  color: #333;
}

.entry h2 a {
  color: #333;
  text-decoration: none;
}

.entry h2 a:hover {
  color: #007bff;
}

.entry p {
  line-height: 1.6;
  color: #666;
  margin-bottom: 10px;
  text-align: justify;
}

.entry img {
  float: left;
  border-radius: 5px;
  margin-right: 15px;
  margin-bottom: 10px;
  max-width: 200px;
  height: auto;
  border: 1px solid #e0e0e0;
}

/* Article detail page - larger image display */
.entry img.article-detail {
  float: none;
  display: block;
  max-width: 100%;
  width: auto;
  height: auto;
  margin: 20px 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.entry .right-img {
  float: right;
}

/* Divider */
.divider {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 30px 0;
}
/* Supaya judul artikel tidak berwarna link default */
.entry h2 a {
  color: #5a5a5a; /* Warna abu-abu sama kaya teks lain */
  text-decoration: none; /* Hilangin underline */
}

.entry h2 a:visited {
  color: #5a5a5a; /* Tetep abu-abu setelah diklik */
}

.entry h2 a:hover {
  color: #5a5a5a; /* Saat hover tetap abu-abu */
}
/* Table styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  font-size: 14px;
  background: #fff;
  border: 1px solid #e0e0e0;
}

table th, table td {
  border: none;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 500;
  font-size: 13px;
}

table tbody tr:hover {
  background-color: #f8f9fa;
}

table tbody tr:nth-child(even) {
  background-color: #fdfdfd;
}

/* Table content styling */
table td b {
  color: #333;
  font-size: 16px;
  margin-bottom: 5px;
  display: block;
}

table td p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

table td small {
  color: #888;
}

/* Status badge */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 400;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Tombol custom */
.btn-ubah {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 6px;
  background-color: #007bff;
  color: #fff;
  text-decoration: none;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 400;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-ubah:hover {
  background-color: #0056b3;
}

.btn-hapus {
  display: inline-block;
  padding: 6px 12px;
  background-color: #dc3545;
  color: #fff;
  text-decoration: none;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 400;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-hapus:hover {
  background-color: #c82333;
}

/* Tombol umum */
.btn {
  display: inline-block;
  padding: 8px 16px;
  background-color: #007bff;
  color: #fff;
  text-decoration: none;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 400;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn:hover {
  background-color: #0056b3;
}

.btn-large {
  padding: 10px 20px;
  font-size: 14px;
}

.btn-primary {
  background-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
}

/* Form styling */
.form-container {
  background: #fff;
  padding: 20px;
  border: 1px solid #e0e0e0;
  margin: 20px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

/* Admin layout improvements - MAIN CONTENT AREA */
.admin-layout #wrapper {
  flex: 1;
  display: block;
  padding: 0 !important;
  margin: 0;
  width: 100%;
  max-width: none;
  box-sizing: border-box;
}

.admin-layout #main {
  width: 100%;
  float: none;
  padding: 0;
  margin: 0;
  background: #fff;
  box-sizing: border-box;
  max-width: none !important;
}

/* Force full width for all admin content */
.admin-layout #main > * {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Card styling untuk konten */
.content-card {
  background: #fff;
  border: none;
  border-top: 1px solid #e0e0e0;
  padding: 20px;
  margin: 0;
}

/* Admin layout specific content card - PERFECTLY CENTERED */
.admin-layout .content-card {
  border: none;
  padding: 30px;
  margin: 30px auto;
  width: 85%;
  max-width: 1100px;
  background: #fff;
  box-sizing: border-box;
  position: relative;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.admin-layout .content-card h2 {
  padding: 0 0 25px 0;
  margin: 0 0 25px 0;
  background: #fff;
  border-bottom: 3px solid #007bff;
  width: 100%;
  box-sizing: border-box;
  font-size: 26px;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
  letter-spacing: 0.5px;
}

/* Table styling yang disesuaikan dengan navbar biru */
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  background: #fff;
  border: none;
  table-layout: fixed;
}

/* Admin layout specific table styling - MATCH NAVBAR WIDTH EXACTLY */
.admin-layout .table {
  width: 100%;
  border: none;
  margin: 0;
  border-collapse: collapse;
  table-layout: fixed;
  display: table;
  box-sizing: border-box;
}

/* Table styling for perfectly centered layout */
.admin-layout .content-card .table {
  width: 100%;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid #e9ecef;
}

/* Admin layout table headers - full width aligned with navbar */
.admin-layout .admin-main .table th {
  background: #007bff !important;
  color: #fff !important;
  padding: 12px 20px !important;
  text-align: left !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  border: none !important;
  white-space: nowrap !important;
  box-sizing: border-box !important;
  border-bottom: 1px solid #0056b3 !important;
}

.admin-layout .admin-main .table th:first-child {
  padding-left: 20px !important;
}

.admin-layout .admin-main .table th:last-child {
  padding-right: 20px !important;
}

/* Clean table structure for centered layout */
.admin-layout .table thead tr {
  width: 100%;
  display: table-row;
}

.admin-layout .table thead {
  width: 100%;
  display: table-header-group;
}

/* ===== ADMIN MAIN CONTAINER - FULL WIDTH LAYOUT ===== */

/* Admin main container - override public layout styles */
.admin-layout .admin-main {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  flex: none !important;
  /* Ensure no interference from public page styles */
  float: none !important;
  position: relative !important;
  box-sizing: border-box !important;
}

/* Content card within admin main - full width */
.admin-layout .admin-main .content-card {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 20px !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: #fff !important;
  /* Override any centering or flex styles */
  display: block !important;
  flex: none !important;
  position: relative !important;
  box-sizing: border-box !important;
}

/* Title styling for admin main */
.admin-layout .admin-main .content-card h2 {
  margin: 0 0 20px 0 !important;
  padding: 0 0 15px 0 !important;
  font-size: 24px !important;
  font-weight: 600 !important;
  color: #333 !important;
  border-bottom: 2px solid #007bff !important;
  text-align: left !important;
  background: transparent !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Table within admin main - full width and aligned */
.admin-layout .admin-main .table {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-collapse: collapse !important;
  background: #fff !important;
  border: 1px solid #e0e0e0 !important;
  /* Override any centering or positioning */
  position: relative !important;
  left: auto !important;
  right: auto !important;
  transform: none !important;
  box-sizing: border-box !important;
}

/* Admin layout table cells - full width layout */
.admin-layout .admin-main .table td {
  padding: 12px 20px !important;
  border-bottom: 1px solid #f0f0f0 !important;
  vertical-align: top !important;
  word-wrap: break-word !important;
  border-left: none !important;
  border-right: none !important;
  box-sizing: border-box !important;
  background: #fff !important;
}

.admin-layout .admin-main .table td:first-child {
  padding-left: 20px !important;
  font-weight: 600 !important;
  color: #495057 !important;
}

.admin-layout .admin-main .table td:last-child {
  padding-right: 20px !important;
}

/* Hover effect for table rows in admin main */
.admin-layout .admin-main .table tbody tr:hover td {
  background: #f8f9fa !important;
}

/* ===== ADMIN FOOTER POSITIONING ===== */

/* Admin main section - take remaining space */
.admin-layout #main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Admin main content - simple layout */
.admin-layout .admin-main {
  display: block;
  width: 100%;
  box-sizing: border-box;
}

/* Footer positioning - sticky footer with proper height */
.admin-layout footer {
  margin-top: auto;
  background: #495057;
  color: #fff;
  text-align: center;
  padding: 25px 20px;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-layout footer p {
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.5px;
}

/* Fallback for non-admin layouts */
.table th {
  background: #007bff;
  color: #fff;
  padding: 12px 20px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  border: none;
  white-space: nowrap;
}

.table td {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
  word-wrap: break-word;
  border-left: none;
  border-right: none;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.table tbody tr:hover td {
  background-color: #f8f9fa;
}

/* Styling khusus untuk kolom */
.table td:first-child {
  text-align: center;
  font-weight: 600;
  color: #007bff;
}

.table td:nth-child(2) strong {
  display: block;
  margin-bottom: 4px;
  color: #333;
  line-height: 1.3;
}

.table td:nth-child(2) small {
  display: block;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.table td:nth-child(3) {
  text-align: center;
}

.table td:nth-child(4) {
  text-align: center;
  white-space: nowrap;
}

/* Header improvements */
header {
  background: #ffffff;
  color: #333;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

header h1 {
  color: #333;
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

/* Page title styling */
h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  #container {
    width: 100%;
    margin: 0;
    box-shadow: none;
  }

  #main {
    padding: 15px;
  }

  .content-card {
    padding: 20px 15px;
  }

  table {
    font-size: 12px;
  }

  table th, table td {
    padding: 10px 8px;
  }

  .btn-ubah, .btn-hapus {
    padding: 6px 10px;
    font-size: 11px;
    margin-bottom: 5px;
    display: block;
    text-align: center;
  }

  .form-group input,
  .form-group textarea {
    padding: 10px;
  }

  nav a {
    padding: 12px 15px;
    font-size: 12px;
  }

  header h1 {
    font-size: 22px;
  }
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255,255,255,.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success/Error messages */
.alert {
  padding: 15px 20px;
  margin: 20px 0;
  border-radius: 6px;
  font-weight: 500;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

/* Footer improvements */
footer {
  background: #2c3e50;
  color: #ecf0f1;
  text-align: center;
  padding: 20px;
  margin-top: 40px;
}

footer p {
  margin: 0;
  font-size: 14px;
}

/* Login Page Styling */
#login-wrapper {
  max-width: 400px;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

#login-wrapper h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 28px;
}

.mb-3 {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #555;
}

#login-wrapper .form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

#login-wrapper .form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

#login-wrapper .btn-primary {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  font-weight: bold;
  background-color: #007bff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

#login-wrapper .btn-primary:hover {
  background-color: #0056b3;
}

#login-wrapper .alert-danger {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
}

/* Login page body styling */
body {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Form Search Styling */
.form-search {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
  align-items: center;
}

.form-search input[type="text"] {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-search input[type="text"]:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-search select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  min-width: 150px;
  transition: border-color 0.3s;
}

.form-search select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-search .btn {
  padding: 8px 16px;
  font-size: 14px;
  white-space: nowrap;
}

/* Modern Card-Based Pagination Styling */
.pagination {
  margin: 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.pagination ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 12px;
  align-items: center;
  justify-content: center;
}

.pagination li {
  margin: 0;
  display: flex;
}

/* Base styling for all pagination buttons */
.pagination li a,
.pagination li span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-sizing: border-box;
}

/* Inactive page numbers - light gray background */
.pagination li a {
  background: #f8f9fa;
  color: #495057;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

/* Hover effect for inactive pages */
.pagination li a:hover {
  background: #e9ecef;
  color: #212529;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #dee2e6;
}

/* Active page - blue background with white text */
.pagination li.active span {
  background: #007bff;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 3px 8px rgba(0, 123, 255, 0.4);
  border: 1px solid #007bff;
  position: relative;
}

/* Disabled state */
.pagination li.disabled span {
  background: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pagination li.disabled span:hover {
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Navigation buttons (Previous/Next) styling */
.pagination li a[rel="prev"],
.pagination li a[rel="next"] {
  width: auto;
  padding: 0 16px;
  font-weight: 500;
}

/* First and Last buttons */
.pagination li:first-child a,
.pagination li:last-child a {
  width: auto;
  padding: 0 12px;
  font-weight: 500;
}

/* Dots styling for pagination */
.pagination li.dots span {
  background: transparent;
  color: #6c757d;
  border: none;
  box-shadow: none;
  cursor: default;
  font-weight: bold;
}

.pagination li.dots span:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
}

/* Pagination Info */
.pagination-info {
  text-align: center;
  margin: 20px 0 15px 0;
}

.pagination-info p {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
  border: 1px solid #e9ecef;
}

/* Pagination container improvements */
.pagination-container {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #e9ecef;
}

/* Focus states for accessibility */
.pagination li a:focus,
.pagination li span:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Active state improvements */
.pagination li.active span {
  position: relative;
  overflow: hidden;
}

.pagination li.active span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.pagination li.active span:hover::before {
  transform: translateX(100%);
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination ul {
    gap: 8px;
    flex-wrap: wrap;
  }

  .pagination li a,
  .pagination li span {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }

  .pagination li a[rel="prev"],
  .pagination li a[rel="next"],
  .pagination li:first-child a,
  .pagination li:last-child a {
    padding: 0 10px;
    font-size: 12px;
  }

  .pagination-info p {
    font-size: 13px;
  }
}

