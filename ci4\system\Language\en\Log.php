<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Log language settings
return [
    'invalidLogLevel'    => '"{0}" is an invalid log level.',
    'invalidMessageType' => 'The given message type "{0}" is not supported.',
];
