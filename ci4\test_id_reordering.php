<?php

// Test script to verify ID reordering functionality
require_once 'vendor/autoload.php';

use Config\Database;
use App\Models\ArtikelModel;

// Initialize CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

echo "=== Testing ID Reordering System ===\n\n";

$model = new ArtikelModel();

// Show current state
echo "Current articles:\n";
$articles = $model->orderBy('id', 'ASC')->findAll();
foreach ($articles as $article) {
    echo "ID: {$article['id']}, Display Order: {$article['display_order']}, Title: {$article['judul']}\n";
}

echo "\n--- Testing getNextAvailableId() ---\n";
$nextId = $model->getNextAvailableId();
echo "Next available ID: $nextId\n";

echo "\n--- Testing reorderIds() ---\n";
echo "Before reordering:\n";
$db = Database::connect();
$result = $db->query("SELECT id FROM artikel ORDER BY id ASC")->getResultArray();
$ids = array_column($result, 'id');
echo "Current IDs: " . implode(', ', $ids) . "\n";

// Run the reordering
$model->reorderIds();

echo "After reordering:\n";
$result = $db->query("SELECT id FROM artikel ORDER BY id ASC")->getResultArray();
$ids = array_column($result, 'id');
echo "New IDs: " . implode(', ', $ids) . "\n";

// Check auto-increment value
$result = $db->query("SHOW TABLE STATUS LIKE 'artikel'")->getRow();
echo "Auto-increment value: " . $result->Auto_increment . "\n";

echo "\n=== Test Complete ===\n";
