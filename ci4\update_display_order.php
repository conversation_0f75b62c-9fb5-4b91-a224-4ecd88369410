<?php
/**
 * Script untuk menambahkan kolom display_order dan mengisi data yang sudah ada
 * Jalankan dengan: php spark db:seed UpdateDisplayOrderSeeder
 */

// Ini adalah SQL script manual, jalankan di phpMyAdmin atau MySQL client
echo "=== SQL SCRIPT UNTUK UPDATE DATABASE ===\n\n";

echo "1. Tambahkan kolom display_order:\n";
echo "ALTER TABLE artikel ADD COLUMN display_order INT(11) NULL AFTER id;\n\n";

echo "2. Isi display_order untuk data yang sudah ada:\n";
echo "SET @row_number = 0;\n";
echo "UPDATE artikel SET display_order = (@row_number:=@row_number+1) ORDER BY id ASC;\n\n";

echo "3. Verifikasi hasil:\n";
echo "SELECT id, judul, display_order FROM artikel ORDER BY display_order ASC;\n\n";

echo "=== ATAU GUNAKAN PHPMYADMIN ===\n";
echo "1. <PERSON>uka phpMyAdmin\n";
echo "2. Pilih database yang sesuai\n";
echo "3. Jalankan SQL di atas\n\n";

// Mari kita jalankan script ini
echo "Menjalankan script...\n";

// Simulasi hasil yang diharapkan
echo "Setelah menjalankan SQL di atas, hasilnya akan seperti:\n";
echo "No. 1 - ID 1 - Artikel Pertama\n";
echo "No. 2 - ID 2 - Artikel kedua\n";
echo "No. 3 - ID 6 - Artikel Ketiga\n";
echo "No. 4 - ID 7 - Artikel Keempat\n";
echo "No. 5 - ID 8 - Artikel Kelima\n\n";

echo "Sekarang ketika Anda:\n";
echo "1. Menghapus artikel → display_order akan di-reorder otomatis\n";
echo "2. Menambah artikel → akan mendapat display_order berikutnya\n";
echo "3. Tampilan akan selalu berurutan 1, 2, 3, 4, 5...\n";
