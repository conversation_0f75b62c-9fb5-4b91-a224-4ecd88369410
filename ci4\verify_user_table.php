<?php
/**
 * Script untuk memverifikasi tabel user
 */

// Load CodeIgniter
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Load database
$db = \Config\Database::connect();

try {
    echo "=== VERIFIKASI TABEL USER ===\n\n";
    
    // 1. Cek struktur tabel
    echo "1. Struktur Tabel User:\n";
    $fields = $db->getFieldData('user');
    
    foreach ($fields as $field) {
        echo "   - {$field->name}: {$field->type}";
        if ($field->max_length) {
            echo "({$field->max_length})";
        }
        if ($field->primary_key) {
            echo " [PRIMARY KEY]";
        }
        if ($field->nullable === false) {
            echo " [NOT NULL]";
        }
        echo "\n";
    }
    
    // 2. Cek data user
    echo "\n2. Data User yang Tersedia:\n";
    $users = $db->query("SELECT id, username, useremail FROM user")->getResultArray();
    
    if (!empty($users)) {
        echo "   ID | Username | Email\n";
        echo "   ---|----------|------\n";
        foreach ($users as $user) {
            echo "   {$user['id']}  | {$user['username']} | {$user['useremail']}\n";
        }
    } else {
        echo "   Tidak ada data user.\n";
    }
    
    // 3. Cek total user
    $total = $db->query("SELECT COUNT(*) as total FROM user")->getRow()->total;
    echo "\n3. Total User: {$total}\n";
    
    echo "\n✅ VERIFIKASI SELESAI!\n";
    echo "Tabel user telah dibuat dengan benar dan berisi data sample.\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}
