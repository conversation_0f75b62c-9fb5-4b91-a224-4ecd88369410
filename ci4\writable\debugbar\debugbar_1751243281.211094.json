{"url": "http://localhost:8080/index.php/admin/artikel/reorder-ids", "method": "GET", "isAJAX": false, "startTime": **********.05061, "totalTime": 134.6, "totalMemory": "5.532", "segmentDuration": 20, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.057112, "duration": 0.025303125381469727}, {"name": "Required Before Filters", "component": "Timer", "start": **********.082416, "duration": 0.003551959991455078}, {"name": "Routing", "component": "Timer", "start": **********.085975, "duration": 0.005918979644775391}, {"name": "Before Filters", "component": "Timer", "start": **********.09229, "duration": 0.006078958511352539}, {"name": "Controller", "component": "Timer", "start": **********.098373, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.098374, "duration": 0.0012960433959960938}, {"name": "After Filters", "component": "Timer", "start": **********.18415, "duration": 1.3113021850585938e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.184171, "duration": 0.001132965087890625}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(17 total Queries, 17 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `id` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:675", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:90", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:90", "qid": "ca785c18f1ef89f332db73f8ae53418c"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "SET FOREIGN_KEY_CHECKS = 0", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:99", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:99", "qid": "d610675b1ebae28c6526e3e92ab604bd"}, {"hover": "", "class": "", "duration": "14.41 ms", "sql": "CREATE TEMPORARY TABLE artikel_temp <strong>LIKE</strong> artikel", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:102", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:102", "qid": "863ea6c69de80168aa7f4aae33ab5a6b"}, {"hover": "", "class": "", "duration": "0.91 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (1, 1, &#039;Harapan Baru Manchester United di Musim Depan&#039;, &#039;Manchester United terus membangun skuad yang lebih solid untuk menghadapi musim kompetisi yang akan datang. Dengan beberapa rekrutan baru dan perbaikan strategi permainan, para penggemar berharap klub ini bisa kembali bersaing di papan atas Liga Inggris. Meski musim sebelumnya penuh tantangan, optimisme tetap tinggi di kalangan suporter bahwa Setan Merah bisa bangkit dan kembali menunjukkan performa terbaik mereka di bawah arahan pelatih yang semakin matang.\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-1&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "659c90cfaf2a4388d782787f80489919"}, {"hover": "", "class": "", "duration": "1.03 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (2, 2, &#039;<PERSON><PERSON>anan Ma<PERSON> Jadi PR Besar MU&#039;, &#039;Meskipun lini serang Manchester United cukup menjanjikan, pertahanan mereka masih menjadi titik lemah. Kerap kebobolan di menit-menit akhir membuat tim kehilangan poin penting. Per<PERSON> adanya evaluasi mendalam dan mungkin penambahan pemain bertahan untuk memperkuat lini belakang.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-2&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "dbdb0c513d601d8d531343279254a7c0"}, {"hover": "", "class": "", "duration": "0.96 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (3, 3, &#039;Dukungan Suporter Setan Merah Tak <PERSON> Padam&#039;, &#039;Terlepas dari naik turunnya performa tim, suporter Manchester United tetap setia mendukung dari tribun stadion maupun media sosial. Semangat dan loyalitas mereka menjadi motivasi besar bagi para pemain untuk terus berjuang membawa kejayaan kembali ke Old Trafford.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-3&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "40820144b45fb4c6b7d37e6e832374f3"}, {"hover": "", "class": "", "duration": "0.74 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (4, 4, &#039;Bursa Transfer, MU Incar Gelandang Kreatif&#039;, &#039;Menjelang pembukaan bursa transfer, Manchester United dikabarkan tengah mengincar gelandang kreatif untuk memperkuat lini tengah. Kehadiran pemain dengan visi dan kreativitas tinggi dinilai penting untuk mendukung pola permainan menyerang yang diusung pelatih. Fans berharap langkah ini bisa membuat tim lebih kompetitif musim depan.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-4&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "15ca71e44b9c9a097b0ac537592c3e67"}, {"hover": "", "class": "", "duration": "0.85 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (5, 5, &#039;<PERSON><PERSON> Cunha Resmi Gabung Manchester United&#039;, &#039;Manchester United akhirnya resmi mendatangkan Matheus Cunha dari Wolves dengan nilai transfer mencapai £62,5 juta. Kehadiran pemain asal Brasil ini diharapkan dapat menambah variasi serangan Setan Merah yang selama ini kurang konsisten. Cunha dikenal sebagai penyerang serba bisa dan dinilai cocok dengan gaya bermain R<PERSON>.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-5&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "a3927daf7c4fe309857c200d21d76b4e"}, {"hover": "", "class": "", "duration": "0.66 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (6, 6, &#039;Manchester United Serius Incar Bryan <PERSON>&#039;, &#039;MU dikabarkan telah menaikkan tawaran mereka menjadi £60 juta untuk mendapatkan Bryan <PERSON>. <PERSON><PERSON>in sayap asal Kamerun ini tampil impresif musim lalu dan dianggap bisa membawa kecepatan serta kreativitas baru di lini depan. Jika transfer ini sukses, <PERSON>beumo akan menjadi rekrutan besar ketiga MU musim panas ini.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-6&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "b49e747e073f08b6fbfe1fad09f0322e"}, {"hover": "", "class": "", "duration": "0.65 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (7, 7, &#039;Ramsdale Jadi Opsi Pengganti Onana&#039;, &#039;Manajemen MU mulai mempertimbangkan nama Aaron <PERSON>dale sebagai calon pengganti Andre <PERSON> yang tampil inkonsisten musim lalu. Ramsdale saat ini tersingkir dari posisi utama di Arsenal, dan kep<PERSON>han ke Old Trafford bisa jadi langkah baru baginya. Situasi ini akan menarik untuk diikuti dalam waktu dekat.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-7&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "3597b13637b3d1a80fb106408a331157"}, {"hover": "", "class": "", "duration": "0.77 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (8, 8, &#039;<PERSON><PERSON>wal Pra-Musim Manchester United Resmi Diumumkan&#039;, &#039;Manchester United akan memulai tur pra-musim mereka di Stockholm melawan Leeds pada 19 Juli, sebelum melanjutkan perjalanan ke Amerika Serikat untuk menghadapi West Ham, Bournemouth, dan Everton. Laga penutup akan digelar di Old Trafford melawan Fiorentina sebelum Liga Inggris musim baru dimulai melawan Arsenal.\\r\\n\\r\\n&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-8&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "34a23db28fe68896cbf7da5a11ddfdf6"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (9, 9, &#039; Stadion Baru Manchester United Masuk Tahap Rencana Serius&#039;, &#039;Proyek pembangunan stadion baru yang dijuluki “New Trafford” mulai masuk tahap perencanaan serius. Stadion ini dirancang menampung hingga 100.000 penonton dan menjadi simbol era baru klub. Meski belum dimulai pembangunannya, suporter menyambut positif kabar ini sebagai bagian dari pembaruan besar di tubuh MU.&#039;, &#039;std mu.jpeg&#039;, &#039;1&#039;, &#039;0&#039;, &#039;Stadion-Baru-Manchester-United-Masuk-Tahap-Rencana-Serius&#039;)", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:113", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:113", "qid": "3973e12963cf1967a15249b04bcd20a9"}, {"hover": "", "class": "", "duration": "3.37 ms", "sql": "DELETE <strong>FROM</strong> artikel", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:119", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:119", "qid": "a249df9911e5ef2b3d8690564fc1281d"}, {"hover": "", "class": "", "duration": "6.37 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> artikel <strong>SELECT</strong> * <strong>FROM</strong> artikel_temp", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:120", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:120", "qid": "445f081023003ddb8db14bec7441405d"}, {"hover": "", "class": "", "duration": "2.02 ms", "sql": "DROP TEMPORARY TABLE artikel_temp", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:121", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:121", "qid": "5a75ae3ce2ad29d83f11cdd540176c7b"}, {"hover": "", "class": "", "duration": "7.17 ms", "sql": "ALTER TABLE artikel AUTO_INCREMENT = 10", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:125", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:125", "qid": "65d5daafcfb2a02197e5ca6299b1f650"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "SET FOREIGN_KEY_CHECKS = 1", "trace": [{"file": "APPPATH\\Models\\ArtikelModel.php:128", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Controllers\\Artikel.php:287", "function": "        App\\Models\\ArtikelModel->reorderAllIds()", "index": "  2    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->reorderIds()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\lab11_ci\\ci4\\public\\index.php"], "function": "        require_once()", "index": "  9    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:128", "qid": "10ac3bb76b0c8ddfa24abb0f1325219f"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.111439, "duration": "0.025469"}, {"name": "Query", "component": "Database", "start": **********.138589, "duration": "0.000411", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `id` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.141061, "duration": "0.000219", "query": "SET FOREIGN_KEY_CHECKS = 0"}, {"name": "Query", "component": "Database", "start": **********.14131, "duration": "0.014405", "query": "CREATE TEMPORARY TABLE artikel_temp <strong>LIKE</strong> artikel"}, {"name": "Query", "component": "Database", "start": **********.155779, "duration": "0.000913", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (1, 1, &#039;Harapan Baru Manchester United di Musim Depan&#039;, &#039;Manchester United terus membangun skuad yang lebih solid untuk menghadapi musim kompetisi yang akan datang. Dengan beberapa rekrutan baru dan perbaikan strategi permainan, para penggemar berharap klub ini bisa kembali bersaing di papan atas Liga Inggris. Meski musim sebelumnya penuh tantangan, optimisme tetap tinggi di kalangan suporter bahwa Setan Merah bisa bangkit dan kembali menunjukkan performa terbaik mereka di bawah arahan pelatih yang semakin matang.\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-1&#039;)"}, {"name": "Query", "component": "Database", "start": **********.156743, "duration": "0.001026", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (2, 2, &#039;<PERSON><PERSON>anan Ma<PERSON> Jadi PR Besar MU&#039;, &#039;Meskipun lini serang Manchester United cukup menjanjikan, pertahanan mereka masih menjadi titik lemah. Kerap kebobolan di menit-menit akhir membuat tim kehilangan poin penting. Per<PERSON> adanya evaluasi mendalam dan mungkin penambahan pemain bertahan untuk memperkuat lini belakang.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-2&#039;)"}, {"name": "Query", "component": "Database", "start": **********.157808, "duration": "0.000963", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (3, 3, &#039;Dukungan Suporter Setan Merah Tak <PERSON> Padam&#039;, &#039;Terlepas dari naik turunnya performa tim, suporter Manchester United tetap setia mendukung dari tribun stadion maupun media sosial. Semangat dan loyalitas mereka menjadi motivasi besar bagi para pemain untuk terus berjuang membawa kejayaan kembali ke Old Trafford.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-3&#039;)"}, {"name": "Query", "component": "Database", "start": **********.158808, "duration": "0.000738", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (4, 4, &#039;Bursa Transfer, MU Incar Gelandang Kreatif&#039;, &#039;Menjelang pembukaan bursa transfer, Manchester United dikabarkan tengah mengincar gelandang kreatif untuk memperkuat lini tengah. Kehadiran pemain dengan visi dan kreativitas tinggi dinilai penting untuk mendukung pola permainan menyerang yang diusung pelatih. Fans berharap langkah ini bisa membuat tim lebih kompetitif musim depan.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-4&#039;)"}, {"name": "Query", "component": "Database", "start": **********.159588, "duration": "0.000853", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (5, 5, &#039;<PERSON><PERSON> Cunha Resmi Gabung Manchester United&#039;, &#039;Manchester United akhirnya resmi mendatangkan Matheus Cunha dari Wolves dengan nilai transfer mencapai £62,5 juta. Kehadiran pemain asal Brasil ini diharapkan dapat menambah variasi serangan Setan Merah yang selama ini kurang konsisten. Cunha dikenal sebagai penyerang serba bisa dan dinilai cocok dengan gaya bermain R<PERSON>.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-5&#039;)"}, {"name": "Query", "component": "Database", "start": **********.160486, "duration": "0.000658", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (6, 6, &#039;Manchester United Serius Incar Bryan <PERSON>&#039;, &#039;MU dikabarkan telah menaikkan tawaran mereka menjadi £60 juta untuk mendapatkan Bryan <PERSON>. <PERSON><PERSON>in sayap asal Kamerun ini tampil impresif musim lalu dan dianggap bisa membawa kecepatan serta kreativitas baru di lini depan. Jika transfer ini sukses, <PERSON>beumo akan menjadi rekrutan besar ketiga MU musim panas ini.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-6&#039;)"}, {"name": "Query", "component": "Database", "start": **********.161187, "duration": "0.000650", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (7, 7, &#039;Ramsdale Jadi Opsi Pengganti Onana&#039;, &#039;Manajemen MU mulai mempertimbangkan nama Aaron <PERSON>dale sebagai calon pengganti Andre <PERSON> yang tampil inkonsisten musim lalu. Ramsdale saat ini tersingkir dari posisi utama di Arsenal, dan kep<PERSON>han ke Old Trafford bisa jadi langkah baru baginya. Situasi ini akan menarik untuk diikuti dalam waktu dekat.&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-7&#039;)"}, {"name": "Query", "component": "Database", "start": **********.161879, "duration": "0.000768", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (8, 8, &#039;<PERSON><PERSON>wal Pra-Musim Manchester United Resmi Diumumkan&#039;, &#039;Manchester United akan memulai tur pra-musim mereka di Stockholm melawan Leeds pada 19 Juli, sebelum melanjutkan perjalanan ke Amerika Serikat untuk menghadapi West Ham, Bournemouth, dan Everton. Laga penutup akan digelar di Old Trafford melawan Fiorentina sebelum Liga Inggris musim baru dimulai melawan Arsenal.\\r\\n\\r\\n&#039;, &#039;&#039;, &#039;2&#039;, &#039;1&#039;, &#039;artikel-sample-8&#039;)"}, {"name": "Query", "component": "Database", "start": **********.16269, "duration": "0.000713", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel_temp (id, display_order, judul, isi, gambar, id_kategori, status, slug) <strong>VALUES</strong> (9, 9, &#039; Stadion Baru Manchester United Masuk Tahap Rencana Serius&#039;, &#039;Proyek pembangunan stadion baru yang dijuluki “New Trafford” mulai masuk tahap perencanaan serius. Stadion ini dirancang menampung hingga 100.000 penonton dan menjadi simbol era baru klub. Meski belum dimulai pembangunannya, suporter menyambut positif kabar ini sebagai bagian dari pembaruan besar di tubuh MU.&#039;, &#039;std mu.jpeg&#039;, &#039;1&#039;, &#039;0&#039;, &#039;Stadion-Baru-Manchester-United-Masuk-Tahap-Rencana-Serius&#039;)"}, {"name": "Query", "component": "Database", "start": **********.163435, "duration": "0.003365", "query": "DELETE <strong>FROM</strong> artikel"}, {"name": "Query", "component": "Database", "start": **********.166833, "duration": "0.006366", "query": "<strong>INSERT</strong> <strong>INTO</strong> artikel <strong>SELECT</strong> * <strong>FROM</strong> artikel_temp"}, {"name": "Query", "component": "Database", "start": **********.173252, "duration": "0.002021", "query": "DROP TEMPORARY TABLE artikel_temp"}, {"name": "Query", "component": "Database", "start": **********.175309, "duration": "0.007170", "query": "ALTER TABLE artikel AUTO_INCREMENT = 10"}, {"name": "Query", "component": "Database", "start": **********.18254, "duration": "0.000253", "query": "SET FOREIGN_KEY_CHECKS = 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n10 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\public\\\\index.php')"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n10 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\public\\\\index.php')"}, {"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 150 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RedirectResponse.php", "name": "RedirectResponse.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Artikel.php", "name": "Artikel.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\Auth.php", "name": "Auth.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 150, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Artikel", "method": "reorderIds", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Page::services"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\Artikel::index"}, {"method": "GET", "route": "artikel/(.*)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "admin/artikel/reorder-ids", "handler": "\\App\\Controllers\\Artikel::reorderIds"}, {"method": "GET", "route": "admin/artikel/test-ids", "handler": "\\App\\Controllers\\Artikel::testIds"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "10.22", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.37", "count": 17}}}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.07219, "duration": 0.010221004486083984}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.139007, "duration": 2.9802322387695312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.141285, "duration": 1.811981201171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.155722, "duration": 2.8133392333984375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.156696, "duration": 2.09808349609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.157773, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.158775, "duration": 1.5020370483398438e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.159551, "duration": 1.71661376953125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.160445, "duration": 2.09808349609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.161148, "duration": 1.7881393432617188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.161841, "duration": 1.811981201171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.162651, "duration": 1.7881393432617188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.163408, "duration": 1.7881393432617188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.166805, "duration": 1.9073486328125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.173206, "duration": 3.2901763916015625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.175277, "duration": 2.09808349609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.182487, "duration": 3.600120544433594e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.182797, "duration": 2.3126602172851562e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1751243257</pre>", "_ci_previous_url": "http://localhost:8080/index.php/admin/artikel?page=2", "user_id": "4", "user_name": "admin", "user_email": "<EMAIL>", "logged_in": "<pre>1</pre>", "success": "ID artikel berhasil diatur ulang secara berurutan.", "__ci_vars": "<pre>Array\n(\n    [success] =&gt; new\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/admin/artikel?page=2", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id,id-ID;q=0.9,en-US;q=0.8,en;q=0.7,su;q=0.6", "Cookie": "ci_session=227b33e293da7f15ed2510292dd7bd63"}, "cookies": {"ci_session": "227b33e293da7f15ed2510292dd7bd63"}, "request": "HTTP/1.1", "response": {"statusCode": 302, "reason": "Found", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8", "Location": "http://localhost:8080/index.php/admin/artikel"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}